"use client";

import { cn } from "@workspace/ui/lib/utils";
import {
  segmentedContainerClasses,
  segmentedItemBaseClasses,
} from "@workspace/ui/components/tabs";

function ButtonGroup({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      role="group"
      data-slot="button-group"
      className={cn(segmentedContainerClasses, className)}
      {...props}
    />
  );
}

function ButtonGroupItem({
  className,
  children,
  type = "button",
  ...props
}: React.ComponentProps<"button">) {
  return (
    <button
      data-slot="button-group-item"
      type={type}
      className={cn(
        segmentedItemBaseClasses,
        "active:bg-primary not-active:bg-gradient-to-t not-active:inset-shadow-xs not-active:inset-shadow-ring active:inset-shadow-sm active:inset-shadow-orange-800/50 transition-none active:[&>*]:translate-y-px",
        className,
      )}
      {...props}
    >
      <div>{children}</div>
    </button>
  );
}

export { ButtonGroup, ButtonGroupItem };
